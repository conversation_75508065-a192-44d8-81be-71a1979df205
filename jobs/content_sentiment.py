from typing import Optional, List
from pydantic import BaseModel
import asyncio
import argparse
import json
import re
import datetime

from jobs.base_job import Base<PERSON>ob, JOB_CONFIG_PATH
from lib.common.logger import logger
from lib.storage.database import Database, DBConfig
from lib.common.config import SecretConfig, Config
from lib.llm_service import LLMService, LLMServiceError

RESPONSE_SENTIMENT_MAP = {"positive": 1, "negative": 2, "neutral": 3, "unrelated": 4}
COMMENT_SENTIMENT_MAP = {1: "positive", 2: "negative", 3: "neutral", 4: "unrelated"}


class EnrichConfig(BaseModel):
    days_lookback: int
    source_table: str
    post_source_table: Optional[str] = None
    target_table: str
    content_type: str
    channel: str
    sub_channel: Optional[str] = None
    channel_type: str
    author_type: Optional[str] = None

    @classmethod
    def model_validate(cls, data: dict):
        if data["content_type"] == "comment" and data["post_source_table"] is None:
            raise ValueError("post_source_table must be specified for comment")
        return super().model_validate(data)


class DashboardMetadata(BaseModel):
    source_table: str
    post_id: Optional[str] = None


class DashboardReportData(BaseModel):
    id: str
    timestamp: datetime.datetime
    content: str
    channel: str
    sub_channel: Optional[str] = None
    channel_type: str
    content_type: str
    sentiment_type_id: int
    topics: List[str]
    author_type: str
    metadata: str

    @classmethod
    def model_validate(cls, data: dict, job_config: EnrichConfig):
        # validate metadata
        post_id = data.get("post_id")

        metadata = DashboardMetadata(
            source_table=job_config.source_table, post_id=post_id
        )
        data["metadata"] = json.dumps(metadata.model_dump())

        # change field name
        data["topics"] = data["topic"]

        # override author_type
        if job_config.author_type:
            data["author_type"] = job_config.author_type

        data["sentiment_type_id"] = RESPONSE_SENTIMENT_MAP[data["sentiment"]]
        data.update(
            {
                "channel": job_config.channel,
                "sub_channel": job_config.sub_channel,
                "channel_type": job_config.channel_type,
                "content_type": job_config.content_type,
            }
        )
        return super().model_validate(data)


class SentimentJob(BaseJob):

    POST_ENDPOINT = "/api/sentiment/post"
    COMMENT_ENDPOINT = "/api/sentiment/comment"

    def __init__(
        self,
        channel_name,
        job_config_path: str = JOB_CONFIG_PATH,
        db_config: DBConfig = SecretConfig().get_db_config(),
        llm_service_url: str = SecretConfig().get_llm_service_url(),
        days_lookback: Optional[int] = None,
    ):
        self.channel_name = channel_name
        self.db = Database(db_config)
        self.llm_service = LLMService(llm_service_url)
        self.job_config = self._get_config(channel_name, job_config_path)

        # if days lookback is set, override
        self.job_config.days_lookback = self._get_days_lookback(days_lookback)
        logger.info(f"Days lookback: {self.job_config.days_lookback}")

    def _get_config(self, channel_name, job_config_path: str) -> EnrichConfig:
        config = Config(job_config_path)
        job_config = config.get_config([channel_name, "enrich_config"])
        if not isinstance(job_config, dict):
            raise ValueError("enrich_config must be a dictionary")
        return EnrichConfig.model_validate(job_config)

    def _get_days_lookback(self, days_lookback: Optional[int]) -> int:
        if days_lookback:
            return days_lookback
        else:
            return self.job_config.days_lookback

    def _get_post_data(self) -> list[dict]:
        sql = f"""
            SELECT id, timestamp,content
            FROM {self.job_config.source_table}
            WHERE (timestamp >= (now() - interval '{self.job_config.days_lookback} days')) and content IS NOT NULL
        """
        results = self.db.execute(query=sql)
        logger.info(f"Found {len(results)} results from post source table")
        return results

    def _get_comment_data(self) -> list[dict]:
        # join comment table with post table
        if self.channel_name == "pantip-comment-mention":
            join_column = "mention_id"
        else:
            join_column = "post_id"
        # sql = f"""
        #     SELECT c.id as id, c.timestamp as timestamp, c.content as content, p.content as post
        #     FROM {self.job_config.source_table} c
        #     JOIN {self.job_config.post_source_table} p ON c.{join_column} = p.id
        #     WHERE (c.timestamp >= (now() - interval '{self.job_config.days_lookback} days')) and c.content IS NOT NULL
        # """

        sql = f"""
            SELECT c.id as id, c.timestamp as timestamp, c.content as content, p.content as post, p.id as post_id, array_to_string(p.topics,',') as post_topic, p.author_type as author_type
            FROM {self.job_config.source_table} c
            JOIN {self.job_config.target_table} p ON c.{join_column} = p.id
            WHERE (c.timestamp >= (now() - interval '{self.job_config.days_lookback} days')) and c.content IS NOT NULL and 'Unrelated'!= ANY(p.topics)
        """
        results = self.db.execute(query=sql)

        logger.info(f"Found {len(results)} results from comment source table")
        return results

    def _get_data(self) -> list[dict]:
        # get post data
        if self.job_config.content_type == "post":
            return self._get_post_data()
        else:
            return self._get_comment_data()

    def _get_existing_ids(self, candidate_ids: List[str]) -> set[str]:
        """Get existing IDs from target table for efficient filtering.

        Args:
            candidate_ids: List of IDs to check against the database

        Returns:
            Set of existing IDs
        """
        if not candidate_ids:
            return set()

        # Use IN clause to only check for specific IDs instead of fetching all
        id_placeholders = ", ".join(["%s"] * len(candidate_ids))
        sql = f"""
            SELECT id
            FROM {self.job_config.target_table}
            WHERE id IN ({id_placeholders})
        """
        results = self.db.execute(query=sql, params=candidate_ids)
        return {item["id"] for item in results}

    def _filter_enriched_data(self, data: List[dict]) -> list[dict]:
        """Filter out enriched data efficiently using a single database query."""
        candidate_ids = [item["id"] for item in data]
        existing_ids = self._get_existing_ids(candidate_ids)
        filtered_data = [item for item in data if item["id"] not in existing_ids]
        logger.info(
            f"Filtered {len(data) - len(filtered_data)} already enriched items, {len(filtered_data)} items remaining"
        )
        return filtered_data

    def _format_post_payload(self, data: dict) -> str | None:
        post_content = self._clean_text(data["content"])
        if not post_content:
            return None
        payload = {"post": post_content}
        json_payload = json.dumps(payload, ensure_ascii=False)
        return json_payload

    def _format_comment_payload(self, data: dict) -> str | None:
        comment_content = self._clean_text(data["content"])
        if not comment_content:
            return None
        post_content = self._clean_text(data["post"])
        payload = {
            "post": post_content,
            "post_topic": data["post_topic"].split(","),
            "comment": comment_content,
        }
        json_payload = json.dumps(payload, ensure_ascii=False)
        return json_payload

    def _clean_text(self, text: str) -> str:
        text = self._remove_invisible_unicode(text)
        text = self._remove_hashtags(text)
        text = self._remove_links(text)
        text = self._remove_extra_spaces(text)
        return text

    def _remove_invisible_unicode(self, text: str) -> str:
        # remove invisible unicode but keep thai characters
        return re.sub(
            r"[\u200b\u200c\u200d\u200e\u200f\u202a-\u202e\u2060\uFEFF]",
            "",
            text,
        )

    def _remove_hashtags(self, text: str) -> str:
        # remove hashtag, except #medeze
        # Regex to find hashtags: words starting with #

        # We'll remove all except the one matching keep_tag (case-insensitive)
        keep_tag = "medeze"
        pattern = re.compile(r"#(\S+)", re.IGNORECASE)

        def repl(m):
            tag = m.group(1)
            if tag.lower() == keep_tag.lower():
                return m.group(0)  # keep the hashtag as is
            else:
                return ""  # remove other hashtags

        return pattern.sub(repl, text)

    def _remove_links(self, text: str) -> str:
        return re.sub(r"http\S+", "", text)

    def _remove_extra_spaces(self, text: str) -> str:
        return re.sub(r"\s+", " ", text).strip()

    def _save_to_target_table(self, data: list[dict]):
        self.db.upsert(
            table=self.job_config.target_table, data=data, unique_columns=["id"]
        )
        logger.info(f"Save processed data to target table")

    async def _get_sentiment(self, data: dict) -> dict | None:
        # format payload
        if self.job_config.content_type == "post":
            payload = self._format_post_payload(data)
            endpoint = self.POST_ENDPOINT
        else:
            payload = self._format_comment_payload(data)
            endpoint = self.COMMENT_ENDPOINT

        # call llm service
        try:
            if not payload:
                logger.info(f"Payload is None for {data['id']}")
                return None
            response = await self.llm_service.post(endpoint=endpoint, data=payload)
            if response is None:
                return None
            # add result to original data
            response.update(data)

            return response
        except LLMServiceError as e:
            logger.error(f"LLM service error: {e} for {data['id']}")
            return None

    async def run(self):
        async with self.llm_service:
            # get data from source table
            data = self._get_data()
            logger.info(f"Found {len(data)} total items from source table")

            # filter out already enriched data
            new_data = self._filter_enriched_data(data)

            if not new_data:
                logger.info("No new data to process")
                return

            logger.info(f"Processing {len(new_data)} new items")

            # Create tasks for all new items
            tasks = []
            for item in new_data:
                tasks.append(self._get_sentiment(item))

            # Await all tasks concurrently
            responses = await asyncio.gather(*tasks, return_exceptions=True)

            # Process responses
            results = []
            fail_count = 0
            for i, response in enumerate(responses):
                if isinstance(response, Exception):
                    logger.error(f"Error on request {i}")
                    fail_count += 1
                    continue
                if response is None:
                    fail_count += 1
                    continue
                results.append(response)

            logger.info(
                f"Successfully processed {len(results)} items, failed: {fail_count}"
            )

            if results:
                # Convert to DashboardReportData format
                result_data = [
                    DashboardReportData.model_validate(item, self.job_config)
                    for item in results
                ]

                # save to target table
                self._save_to_target_table(
                    [item.model_dump(mode="json") for item in result_data]
                )

            logger.info(
                f"Job completed. Processed: {len(results)}, Failed: {fail_count}"
            )
            if fail_count > 0:
                raise Exception(f"Failed to process {fail_count} items")


if __name__ == "__main__":
    # Set up argument parser
    parser = argparse.ArgumentParser(
        description="Run sentiment/topic job for a specific channel"
    )
    parser.add_argument(
        "channel_name",
        type=str,
        help="Name of the channel to process sentiment/topic (required)",
    )

    # Parse arguments
    args = parser.parse_args()

    logger.info("Starting the job...")

    job = SentimentJob(channel_name=args.channel_name)
    asyncio.run(job.run())

    logger.info("Job completed successfully.")
