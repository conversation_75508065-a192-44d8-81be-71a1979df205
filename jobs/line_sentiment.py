from jobs.base_job import TransformJob
from lib.common.config import SecretConfig
from lib.storage.database import Database, DBConfig
from lib.llm_service import LLMService, LLMServiceError
from lib.common.logger import logger
import json
from pydantic import BaseModel
import asyncio
import datetime
from typing import List, Optional

RESPONSE_SENTIMENT_MAP = {"positive": 1, "negative": 2, "neutral": 3, "unrelated": 4}


class LineSentimentPayload(BaseModel):
    id: str
    content: str


class LineSentiment(BaseModel):
    id: str
    sentiment_type_id: int
    topics: List[str]

    @classmethod
    def model_validate(cls, data: dict):
        data["sentiment_type_id"] = RESPONSE_SENTIMENT_MAP[data["sentiment"]]
        return super().model_validate(data)


class DashboardMetadata(BaseModel):
    source_table: str
    post_id: Optional[str] = None


class DashboardReportData(BaseModel):
    id: str
    timestamp: datetime.datetime
    content: str
    channel: str = "line"
    sub_channel: Optional[str] = None
    channel_type: str = "private"
    content_type: str = "chat"
    sentiment_type_id: int
    topics: List[str]
    author_type: str = "investor"
    metadata: str

    @classmethod
    def model_validate(cls, data: dict):
        metadata = DashboardMetadata(source_table=data["source_table"])
        data["metadata"] = json.dumps(metadata.model_dump())

        # change field name
        data["sentiment_type_id"] = RESPONSE_SENTIMENT_MAP[data["sentiment"]]

        return super().model_validate(data)


class LineSentimentJob(TransformJob):
    LINE_SENTIMENT_ENDPOINT = "/api/sentiment/line"
    TARGET_TABLE: str = "data_line_openchat"
    DASHBOARD_TABLE: str = "dashboard_report_data"

    def __init__(
        self,
        days_lookback: int = 14,
        llm_service_url: str = SecretConfig().get_llm_service_url(),
        db_config: DBConfig = SecretConfig().get_db_config(),
    ):
        self.days_lookback = days_lookback
        self.db = Database(config=db_config)
        self.llm_service = LLMService(llm_service_url)

    def _get_data(self) -> list[dict]:
        sql = f"""
            SELECT *
            FROM {self.TARGET_TABLE}
            WHERE sentiment_type_id IS NULL
        """
        #     WHERE (timestamp >= (now() - interval '{self.days_lookback} days'))
        # """
        results = self.db.execute(query=sql)
        logger.info(f"Found {len(results)} results from line openchat table")
        return results

    def _group_conversation(self, data: list[dict]) -> dict:
        grouped_data = {}
        for item in data:
            conversation_id = item["conversation_id"]
            if conversation_id not in grouped_data:
                grouped_data[conversation_id] = []
            grouped_data[conversation_id].append(item)
        return grouped_data

    def _prepare_payload(
        self, conversation_data: list[dict]
    ) -> list[LineSentimentPayload]:
        # then sort with content_order
        conversation_data.sort(key=lambda x: x["content_order"])
        # for each item in list, get id, content, content_order.
        payload = [
            LineSentimentPayload(id=item["id"], content=item["content"])
            for item in conversation_data
        ]
        return payload

    async def _get_sentiment(self, conversation_data: list[dict]):
        payload = self._prepare_payload(conversation_data)

        # call llm service
        response = await self.llm_service.post(
            endpoint=self.LINE_SENTIMENT_ENDPOINT,
            data=json.dumps([item.model_dump() for item in payload]),
        )
        return response.get("contents", [])

    def extract(self) -> list[dict]:
        data = self._get_data()
        return data

    async def transform(self, data: list[dict]):
        # group data with field conversation_id and order them with content_order
        conversations = self._group_conversation(data)

        tasks = [self._get_sentiment(data) for data in conversations.values()]
        responses = await asyncio.gather(*tasks)
        # flatten
        responses = [item for sublist in responses for item in sublist]
        return responses

    def _load_line_table(self, transformed_data: list[dict]):
        for item in transformed_data:
            try:
                item = LineSentiment.model_validate(item).model_dump(mode="json")
                self.db.update_row(
                    table=self.TARGET_TABLE,
                    data=item,
                    conditions={"id": item["id"]},
                )
            except:
                logger.error(f"Error updating line table: {item}")
                continue

    def _load_dashboard_table(self, data: list[dict], transformed_data: list[dict]):
        insert_data = []
        # filter out sentiment = 'unrelated'
        filtered_data = [
            item for item in transformed_data if item["sentiment"] != "unrelated"
        ]
        for item in filtered_data:
            # map transform data with original data with id
            original_data = next((d for d in data if d["id"] == item["id"]), None)
            if original_data is None:
                logger.error(f"Cannot find original data for {item}")
                continue

            # update topics and sentiment to original data
            original_data.update(item)
            # add source_table
            original_data["source_table"] = self.TARGET_TABLE

            # convert to dashboard report data
            dashboard_data = DashboardReportData.model_validate(original_data)
            insert_data.append(dashboard_data.model_dump(mode="json"))

        logger.info(f"Inserting {len(insert_data)} data to dashboard table")
        self.db.insert(self.DASHBOARD_TABLE, insert_data)
        logger.info(f"Inserted data to {self.DASHBOARD_TABLE}")

    def load(self, data: list[dict], transformed_data: list[dict]):
        # update line table
        self._load_line_table(transformed_data)
        # insert dashboard table
        self._load_dashboard_table(data, transformed_data)

    async def run(self):
        data = self.extract()
        logger.info(f"Found {len(data)} results from line openchat table")

        transform_data = await self.transform(data)
        logger.info(f"Transformed data: {transform_data}")

        self.load(data, transform_data)


if __name__ == "__main__":
    logger.info("Starting the job...")
    logger.info("Run line sentiment job")
    job = LineSentimentJob()
    asyncio.run(job.run())
    logger.info("Job completed successfully.")
