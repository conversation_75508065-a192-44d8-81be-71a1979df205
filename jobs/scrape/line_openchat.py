from typing import List, Dict, Any, Union
import pendulum
import re
import json
from pydantic import BaseModel
import datetime
import uuid
import asyncio
import argparse

from jobs.base_job import TransformJob
from lib.common.logger import logger
from lib.storage.gcs import GCS
from lib.storage.database import Database
from lib.common.config import SecretConfig
from lib.llm_service import LLMService, LLMServiceError


class LineOpenChatData(BaseModel):
    group_id: str
    conversation_id: str
    timestamp: datetime.datetime
    author_name: str
    content: str
    content_order: int

    @classmethod
    def parse(cls, data: dict):
        # Parse data and format timestamp
        data["timestamp"] = pendulum.parse(
            data["timestamp"], tz="Asia/Bangkok"
        ).in_timezone("UTC")
        data["id"] = str(uuid.uuid4())
        return cls.model_validate(data)


class LineTransformJob(TransformJob):
    LINE_CHAT_MEDEZE_ENDPOINT = "/api/line-message-filter"
    TARGET_TABLE: str = "data_line_openchat"

    def __init__(
        self,
        bucket_name: str,
        file_path: str,
        gcs_credentials_path: str = SecretConfig().get_datalake_bucket_credentials_path(),
        llm_service_url: str = SecretConfig().get_llm_service_url(),
        db_config=SecretConfig().get_db_config(),
    ):
        self.gcs = GCS(credentials_path=gcs_credentials_path, bucket_name=bucket_name)
        self.db = Database(config=db_config)
        self.llm_service = LLMService(llm_service_url, timeout=180)  # 3 minutes timeout
        self.file_path = file_path
        self.line_group_id = self.get_line_group_id(file_path)

    def get_line_group_id(self, file_path: str) -> str:
        return file_path.split("/")[-2]

    def split_text_by_date(self, input_text: str) -> List[dict]:
        result = []
        if not input_text.strip():
            return result

        # Pattern to match date lines (e.g., 2025.06.19 Thursday or Wed, 23/04/2025)
        date_pattern = r"(\d{4}\.\d{2}\.\d{2} \w+|[A-Za-z]{3}, \d{2}/\d{2}/\d{4})"

        # Split content by date headers
        chunks = re.split(date_pattern, input_text)

        # check first chunck if it has date_pattern
        if not re.match(date_pattern, chunks[0]):
            logger.info("No date pattern found in first chunk")
            chunks = chunks[1:]

        # Process pairs of (header, content)
        for i in range(0, len(chunks) - 1, 2):
            if i + 1 < len(chunks):
                header = chunks[i]
                content = chunks[i + 1]

                # Handle different date formats
                if "." in header:  # Format: 2025.06.19 Thursday
                    date_part = header.split()[0].replace(".", "-")
                else:  # Format: Wed, 23/04/2025
                    date_str = header.split(", ")[1]  # Get 23/04/2025
                    day, month, year = date_str.split("/")
                    date_part = f"{year}-{month}-{day}"

                full_text = content.strip()

                if full_text:  # Only add if there's actual content
                    item = {"date_part": date_part, "full_text": full_text}
                    result.append(item)

        return result

    def has_medeze(self, text: str) -> bool:
        print(text)
        keywords = ["medeze", "เมดีซ"]
        text_lower = text.lower()
        return any(keyword in text_lower for keyword in keywords)

    def replace_ids_with_uuid(self, data: List[dict]) -> List[dict]:
        id_map = {}
        for row in data:
            conv_id = row["conversation_no"]
            if conv_id not in id_map:
                id_map[conv_id] = str(uuid.uuid4())
            row["conversation_id"] = id_map[conv_id]
        return data

    def transform_response(
        self, response: List[dict], date_part: str
    ) -> List[LineOpenChatData]:
        # replace with uuid
        response = self.replace_ids_with_uuid(response)
        # add date_part in front of timestamp and convert from BKK time to UTC
        for item in response:
            item["timestamp"] = f"{date_part} {item['timestamp']}"
            item["group_id"] = self.line_group_id

        result = [LineOpenChatData.parse(item) for item in response]
        return result

    def clean_response(
        self, response: List[List[LineOpenChatData]]
    ) -> List[LineOpenChatData]:
        # remove None
        clean_response_list = [item for item in response if item is not None]
        # flatten the list
        flatten_response = [item for sublist in clean_response_list for item in sublist]

        # Filter out None values and "Photos"
        result = [
            item
            for item in flatten_response
            if item is not None and item.content != "Photos"
        ]

        return result

    async def filter_releated_to_medeze(
        self, date_data: dict
    ) -> List[LineOpenChatData] | None:
        # call llm service
        try:
            result = {}
            date_part = date_data["date_part"]
            raw_text_data = date_data["full_text"]
            payload = {"raw_text": raw_text_data}
            json_payload = json.dumps(payload, ensure_ascii=False)

            if not json_payload:
                logger.info(f"Payload is None for {raw_text_data}")
                return None

            logger.info(f"Sending request to LLM service for {date_part}")
            response = await self.llm_service.post(
                endpoint=self.LINE_CHAT_MEDEZE_ENDPOINT, data=json_payload
            )
            # Extract message_items
            result = response.get("message_items", [])
            if result:
                result = self.transform_response(result, date_part)
                return result
        except LLMServiceError as e:
            logger.error(f"LLM service error: {e} for {raw_text_data}")
            return None

    def get_item_with_max_date_part(
        self, data: List[Dict[str, str]]
    ) -> List[Dict[str, str]]:
        if not data:
            return []

        # Get the item with the maximum date_part
        latest = max(data, key=lambda x: x["date_part"])
        return [latest]

    def extract(self) -> str | None:
        """Read data from GCS bucket."""
        # Get the most recent ingest date
        gcs_path = self.file_path
        # Download from GCS
        # file_text = self.gcs.get_latest_file(gcs_path)
        data_text = self.gcs.read_text_file(gcs_path)
        # print(data_text)
        # Ensure data is a string
        if isinstance(data_text, str):
            return data_text
        return None  # Return an empty list if data is None

    async def transform(
        self,
        input_text: str,
    ):
        """Transform data for The Line chat related to Medeze."""

        # Split text by date
        text_splited_by_date = self.split_text_by_date(input_text=input_text)

        # Max date part
        item_with_max_date_part = self.get_item_with_max_date_part(text_splited_by_date)
        # print(f"this is max date part --------- \n {item_with_max_date_part}")

        # Call LLM service for extract data related to Medeze
        tasks = [self.filter_releated_to_medeze(data) for data in text_splited_by_date]
        data_related_to_medeze = await asyncio.gather(*tasks)

        results = self.clean_response(data_related_to_medeze)

        # return text_splited_by_date
        return results

    def load(self, data: List[LineOpenChatData]):
        """Save transformed data to database."""
        data_dicts = [item.model_dump() for item in data]

        success_count = 0
        for item in data_dicts:
            try:
                rows_affected = self.db.insert(table=self.TARGET_TABLE, data=item)
                success_count += rows_affected
            except Exception as e:
                if "unique" in str(e).lower() or "duplicate" in str(e).lower():
                    logger.warning(
                        f"Skipping duplicate item with id: {item.get('id', 'unknown')}"
                    )
                else:
                    logger.error(f"Error inserting item: {e}")
                    raise

        logger.info(
            f"Transformed data saved to database table: {self.TARGET_TABLE} ({success_count} rows affected)"
        )

    async def run(self):
        """Run the transform job."""
        # Extract data from GCS
        raw_data = self.extract()

        if not raw_data:
            logger.warning("No data found in GCS")
            return

        # check if text raw_data has text medeze
        if not self.has_medeze(raw_data):
            logger.info("No medeze mention found in raw data")
            return

        # Transform data
        transformed_data = await self.transform(raw_data)
        logger.info(f"Found {len(transformed_data)} data related to Medeze")

        # Save to database
        if transformed_data:
            self.load(transformed_data)
        else:
            logger.warning("No data to save after transformation")


if __name__ == "__main__":
    logger.info("Starting the job...")
    logger.info("Run line openchat transform job")
    # Set up argument parser
    parser = argparse.ArgumentParser(
        description="Run ingestion job for a specific channel"
    )
    parser.add_argument("bucket_name", type=str, help="Bucket name (required)")

    parser.add_argument(
        "file_path", type=str, help="Path to the file uploaded (required)"
    )

    # Parse arguments
    args = parser.parse_args()

    logger.info("Starting the job...")
    logger.info(f"Bucket name: {args.bucket_name}")
    logger.info(f"File path: {args.file_path}")
    job = LineTransformJob(bucket_name=args.bucket_name, file_path=args.file_path)
    (asyncio.run(job.run()))
    logger.info("Job completed successfully.")
