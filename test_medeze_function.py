#!/usr/bin/env python3

import sys
import os

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from jobs.scrape.line_openchat import LineTransformJob

def test_has_medeze():
    # Create a test instance
    job = LineTransformJob(bucket_name="test", file_path="test/path")
    
    # Test with the actual file content
    with open("dev/line/data/2025-07-16.txt", "r", encoding="utf-8") as f:
        file_content = f.read()
    
    print(f"File content length: {len(file_content)}")
    print(f"File contains 'medeze' (case insensitive): {'medeze' in file_content.lower()}")
    print(f"File contains 'MEDEZE': {'MEDEZE' in file_content}")
    
    # Test the function
    result = job.has_medeze(file_content)
    print(f"has_medeze function result: {result}")
    
    # Test with simple strings
    test_cases = [
        "This contains MEDEZE",
        "This contains medeze",
        "This contains เมดีซ",
        "This contains nothing",
        ""
    ]
    
    for test_case in test_cases:
        result = job.has_medeze(test_case)
        print(f"Test '{test_case}': {result}")

if __name__ == "__main__":
    test_has_medeze()
