from typing import List, Optional, Any
from pydantic import BaseModel, HttpUrl, Field, ConfigDict
from abc import ABC, abstractmethod
import pendulum


class ApifyInputBuilder(ABC):
    @abstractmethod
    def create_search_term(self, search_items: List[str]) -> str | List[str]:
        """Create the search term from the keyword."""
        pass

    @abstractmethod
    def build(self, search_term: str) -> dict:
        """Build the input configuration for an Apify actor."""
        pass

    # @classmethod
    # def from_channel(cls, channel_name: str, **kwargs: Any) -> "ApifyInputBuilder":
    #     """Factory method to create an input builder based on channel name with custom arguments."""
    #     if channel_name == "facebook-post":
    #         return FacebookPostInputBuilder(**kwargs)
    #     elif channel_name == "facebook-comment":
    #         return FacebookCommentInputBuilder(**kwargs)
    #     elif channel_name == "twitter":
    #         return TwitterInputBuilder(**kwargs)
    #     elif channel_name == "instagram-post":
    #         return InstagramPostInputBuilder(**kwargs)
    #     elif channel_name == "instagram-comment":
    #         return InstagramCommentInputBuilder(**kwargs)
    #     elif channel_name == "youtube-post":
    #         return YoutubePostInputBuilder(**kwargs)
    #     elif channel_name == "youtube-comment":
    #         return YoutubeCommentInputBuilder(**kwargs)
    #     elif channel_name == "tiktok-post":
    #         return TikTokPostInputBuilder(**kwargs)
    #     elif channel_name == "tiktok-comment":
    #         return TikTokCommentInputBuilder(**kwargs)
    #     else:
    #         raise ValueError(f"Unknown channel: {channel_name}")


#############################
# Facebook
#############################
class FacebookPostInput(BaseModel):
    query: str
    start_date: str
    end_date: str
    max_posts: int
    search_type: str = "posts"


class FacebookPostInputBuilder(ApifyInputBuilder):
    def __init__(
        self,
        max_posts: int = 500,
        days_lookback: int = 3,
        start_date: str | None = None,
        end_date: str | None = None,
    ):
        self.max_posts = max_posts
        self.days_lookback = days_lookback
        self.start_date = start_date
        self.end_date = end_date or pendulum.now().to_date_string()

        if not days_lookback and not start_date:
            raise ValueError("Either days_lookback or start_date must be provided")

    def create_search_term(self, search_items: List[str]) -> List[str]:
        return search_items

    def build(self, search_term: str) -> dict:
        start_date = (
            self.start_date
            or pendulum.now().subtract(days=self.days_lookback).to_date_string()
        )
        input_data = FacebookPostInput(
            query=search_term,
            start_date=start_date,
            end_date=self.end_date,
            max_posts=self.max_posts,
        )
        return input_data.model_dump(by_alias=True)


#############################
# Facebook Comment
#############################


class FacebookCommentInput(BaseModel):
    start_urls: List[dict[str, str]] = Field(alias="startUrls")
    include_nested_comments: bool = Field(alias="includeNestedComments", default=True)
    results_limit: int = Field(alias="resultsLimit")
    view_option: str = Field(alias="viewOption", default="RANKED_UNFILTERED")

    model_config = ConfigDict(validate_by_name=True, validate_by_alias=True)


class FacebookCommentInputBuilder(ApifyInputBuilder):
    def __init__(self, results_limit: int = 15000):
        self.results_limit = results_limit

    def create_search_term(self, search_items: List[str]) -> List[str]:
        if not search_items:
            return []
        # Join the list of links with a comma -> Bulk mode
        return [",".join(search_items)]

    def build(self, search_term: str) -> dict:
        input_data = FacebookCommentInput(
            startUrls=[
                {"url": link, "method": "GET"} for link in search_term.split(",")
            ],
            resultsLimit=self.results_limit,
        )
        return input_data.model_dump(by_alias=True)


#############################
# Instagram
#############################


class InstagramPostInput(BaseModel):
    start_urls: List[str] = Field(alias="startUrls")
    until: str  # Returns posts newer than this date.
    max_items: int = Field(alias="maxItems")

    model_config = ConfigDict(validate_by_name=True, validate_by_alias=True)


class InstagramPostInputBuilder(ApifyInputBuilder):
    def __init__(
        self,
        max_items: int = 1000,
        start_date: str | None = None,
        days_lookback: int = 3,
    ):
        self.max_items = max_items
        self.days_lookback = days_lookback
        self.start_date = start_date

        if not days_lookback and not start_date:
            raise ValueError("Either days_lookback or start_date must be provided")

    def create_search_term(self, search_items: List[str]) -> List[str]:
        if not search_items:
            return []

        # Remove space from search items -> Search as hashtag
        search_urls = [
            f"https://www.instagram.com/explore/tags/{item.replace(' ', '')}"
            for item in search_items
        ]
        # Join the list of links with a comma -> Bulk mode
        return [",".join(search_urls)]

    def build(self, search_term: str) -> dict:
        input_data = InstagramPostInput(
            startUrls=[link for link in search_term.split(",")],
            until=(
                self.start_date
                or pendulum.now().subtract(days=self.days_lookback).to_date_string()
            ),
            maxItems=self.max_items,
        )
        return input_data.model_dump(by_alias=True)


#############################
# Instagram Comment
#############################
class InstagramCommentInput(BaseModel):
    start_urls: List[str] = Field(alias="startUrls")
    max_comments: int = Field(alias="maxComments")
    include_replies: bool = Field(alias="includeReplies", default=True)

    model_config = ConfigDict(validate_by_name=True, validate_by_alias=True)


class InstagramCommentInputBuilder(ApifyInputBuilder):
    def __init__(self, max_comments: int = 1000):
        self.max_comments = max_comments

    def create_search_term(self, search_items: List[str]) -> List[str]:
        if not search_items:
            return []
        # Join the list of post URLs with a comma -> Bulk mode
        return [",".join(search_items)]

    def build(self, search_term: str) -> dict:
        input_data = InstagramCommentInput(
            startUrls=[url for url in search_term.split(",")],
            maxComments=self.max_comments,
        )
        return input_data.model_dump(by_alias=True)


#############################
# Youtube
#############################
class YoutubePostInput(BaseModel):
    search_queries: List[str] = Field(alias="searchQueries")
    date_filter: str = Field(alias="dateFilter", default="week")
    sorting_order: str = Field(alias="sortingOrder", default="date")
    max_result_streams: int = Field(alias="maxResultStreams", default=50)
    max_results: int = Field(alias="maxResults", default=50)
    max_results_shorts: int = Field(alias="maxResultsShorts", default=50)
    download_subtitles: bool = Field(alias="downloadSubtitles", default=False)
    has_cc: bool = Field(alias="hasCC", default=False)
    has_location: bool = Field(alias="hasLocation", default=False)
    has_subtitles: bool = Field(alias="hasSubtitles", default=False)
    is_360: bool = Field(alias="is360", default=False)
    is_3d: bool = Field(alias="is3D", default=False)
    is_4k: bool = Field(alias="is4K", default=False)
    is_bought: bool = Field(alias="isBought", default=False)
    is_hd: bool = Field(alias="isHD", default=False)
    is_hdr: bool = Field(alias="isHDR", default=False)
    is_live: bool = Field(alias="isLive", default=False)
    is_vr180: bool = Field(alias="isVR180", default=False)
    prefer_auto_generated_subtitles: bool = Field(
        alias="preferAutoGeneratedSubtitles", default=False
    )
    save_subs_to_kvs: bool = Field(alias="saveSubsToKVS", default=False)

    model_config = ConfigDict(validate_by_name=True, validate_by_alias=True)


class YoutubePostInputBuilder(ApifyInputBuilder):
    def __init__(
        self,
        max_results: int = 50,
        date_filter: str = "week",
        max_results_shorts: int = 50,
        max_results_streams: int = 50,
    ):
        self.max_results = max_results
        self.date_filter = date_filter
        self.max_results_shorts = max_results_shorts
        self.max_results_streams = max_results_streams

    def create_search_term(self, search_items: List[str]) -> List[str]:
        if not search_items:
            return []
        # add double quotes to full-text match search
        return [",".join(f'"{keyword}"' for keyword in search_items)]

    def build(self, search_term: str) -> dict:
        input_data = YoutubePostInput(
            searchQueries=[term for term in search_term.split(",")],
            dateFilter=self.date_filter,
            maxResults=self.max_results,
            maxResultsShorts=self.max_results_shorts,
            maxResultStreams=self.max_results_streams,
        )
        return input_data.model_dump(by_alias=True)


#############################
# Youtube Comment
#############################


class YoutubeCommentInput(BaseModel):
    comments_sort_by: str = Field(alias="commentsSortBy", default="1")  # sort newest
    max_comments: int = Field(alias="maxComments")
    start_urls: List[dict[str, str]] = Field(alias="startUrls")

    model_config = ConfigDict(validate_by_name=True, validate_by_alias=True)


class YoutubeCommentInputBuilder(ApifyInputBuilder):
    def __init__(self, max_comments: int = 1000):
        self.max_comments = max_comments

    def create_search_term(self, search_items: List[str]) -> List[str]:
        if not search_items:
            return []
        # Join the list of video URLs with a comma -> Bulk mode
        return [",".join(search_items)]

    def build(self, search_term: str) -> dict:
        input_data = YoutubeCommentInput(
            startUrls=[{"url": url, "method": "GET"} for url in search_term.split(",")],
            maxComments=self.max_comments,
        )
        return input_data.model_dump(by_alias=True)


#############################
# Twitter (X)
#############################


class TwitterInput(BaseModel):
    search_terms: List[str] = Field(alias="searchTerms")
    max_items: int = Field(alias="maxItems", default=800)
    sort: str = "Latest"
    start: str | None = None
    end: str | None = None

    model_config = ConfigDict(validate_by_name=True, validate_by_alias=True)


class TwitterPostInputBuilder(ApifyInputBuilder):
    def __init__(
        self,
        max_items: int = 800,
        days_lookback: int = 3,
        start_date: str | None = None,
        end_date: str | None = None,
    ):
        self.max_items = max_items
        self.days_lookback = days_lookback
        self.start_date = start_date
        self.end_date = end_date or pendulum.now().to_date_string()

        if not days_lookback and not start_date:
            raise ValueError("Either days_lookback or start_date must be provided")

    def create_search_term(self, search_items: List[str]) -> List[str]:
        if not search_items:
            return []
        return [" OR ".join([f'"{item}"' for item in search_items])]

    def build(self, search_term: str) -> dict:
        # Current : Support only one search term with maximum 800 results
        # API can handle multiple search in case of results > 800
        input_data = TwitterInput(
            searchTerms=search_term.split(","),
            start=(
                self.start_date
                or pendulum.now().subtract(days=self.days_lookback).to_date_string()
            ),
            end=self.end_date,
            maxItems=self.max_items,
        )
        return input_data.model_dump(by_alias=True)


class TwitterCommentInputBuilder(ApifyInputBuilder):
    def __init__(
        self,
        max_items: int = 800,
    ):
        self.max_items = max_items

    def create_search_term(self, search_items: List[str]) -> List[str]:
        if not search_items:
            return []
        # Format each item with conversation_id prefix and join with comma
        formatted_items = [f"conversation_id:{item}" for item in search_items]
        return [",".join(formatted_items)]

    def build(self, search_term: str) -> dict:
        input_data = TwitterInput(
            searchTerms=search_term.split(","), maxItems=self.max_items
        )
        return input_data.model_dump(by_alias=True)


#############################
# Tiktok
#############################


class TikTokPostInput(BaseModel):
    date_range: str = Field(alias="dateRange", default="THIS_WEEK")
    include_search_keywords: bool = Field(alias="includeSearchKeywords", default=True)
    keywords: List[str]
    max_items: int = Field(alias="maxItems", default=1000)
    sort_type: str = Field(alias="sortType", default="DATE_POSTED")

    model_config = ConfigDict(validate_by_name=True, validate_by_alias=True)


class TikTokPostInputBuilder(ApifyInputBuilder):
    def __init__(self, max_items: int = 1000, date_range: str = "THIS_WEEK"):
        self.max_items = max_items
        self.date_range = date_range

    def create_search_term(
        self,
        search_items: List[str],
    ) -> List[str]:
        if not search_items:
            return []
        # Bulk mode
        # Remove space from search items -> Search as hashtag
        return [",".join(item.replace(" ", "") for item in search_items)]

    def build(self, search_term: str) -> dict:
        input_data = TikTokPostInput(
            keywords=[keyword for keyword in search_term.split(",")],
            dateRange=self.date_range,
            maxItems=self.max_items,
        )
        return input_data.model_dump(by_alias=True)


#############################
# Tiktok Comment
#############################


class TikTokCommentInput(BaseModel):
    include_replies: bool = Field(alias="includeReplies", default=True)
    max_items: int = Field(alias="maxItems")
    start_urls: List[str] = Field(alias="startUrls")

    model_config = ConfigDict(validate_by_name=True, validate_by_alias=True)


class TikTokCommentInputBuilder(ApifyInputBuilder):
    def __init__(self, max_items: int = 1000):
        self.max_items = max_items

    def create_search_term(self, search_items: List[str]) -> List[str]:
        if not search_items:
            return []
        # Bulk mode
        return [",".join(search_items)]

    def build(self, search_term: str) -> dict:
        input_data = TikTokCommentInput(
            startUrls=[url for url in search_term.split(",")],
            maxItems=self.max_items,
        )
        return input_data.model_dump(by_alias=True)


class ActorConfig(BaseModel):
    model_config = ConfigDict(arbitrary_types_allowed=True)

    id: str
    memory_mbytes: int
    input_builder: type[ApifyInputBuilder]


ACTORS: dict[str, ActorConfig] = {
    "facebook-post": ActorConfig(
        id="danek/facebook-search-ppr",
        memory_mbytes=1024,
        input_builder=FacebookPostInputBuilder,
    ),
    "facebook-comment": ActorConfig(
        id="apify/facebook-comments-scraper",
        memory_mbytes=1024,
        input_builder=FacebookCommentInputBuilder,
    ),
    "twitter-post": ActorConfig(
        id="apidojo/twitter-scraper-lite",
        memory_mbytes=1024,
        input_builder=TwitterPostInputBuilder,
    ),
    "twitter-comment": ActorConfig(
        id="apidojo/twitter-scraper-lite",
        memory_mbytes=1024,
        input_builder=TwitterCommentInputBuilder,
    ),
    "instagram-post": ActorConfig(
        id="apidojo/instagram-scraper",
        memory_mbytes=256,
        input_builder=InstagramPostInputBuilder,
    ),
    "instagram-comment": ActorConfig(
        id="apidojo/instagram-comments-scraper",
        memory_mbytes=256,
        input_builder=InstagramCommentInputBuilder,
    ),
    "youtube-post": ActorConfig(
        id="streamers/youtube-scraper",
        memory_mbytes=1024,
        input_builder=YoutubePostInputBuilder,
    ),
    "youtube-comment": ActorConfig(
        id="streamers/youtube-comments-scraper",
        memory_mbytes=1024,
        input_builder=YoutubeCommentInputBuilder,
    ),
    "tiktok-post": ActorConfig(
        id="apidojo/tiktok-scraper",
        memory_mbytes=256,
        input_builder=TikTokPostInputBuilder,
    ),
    "tiktok-comment": ActorConfig(
        id="apidojo/tiktok-comments-scraper",
        memory_mbytes=256,
        input_builder=TikTokCommentInputBuilder,
    ),
}


def get_actor_config(
    actor_name: str, **kwargs: Any
) -> tuple[str, int, ApifyInputBuilder]:
    """Get actor configuration by name.

    Args:
        actor_name: Name of the actor configuration to retrieve
        **kwargs: Additional arguments to pass to the input builder

    Returns:
        Tuple of (actor_id, memory_mbytes, input_builder)
    """
    if actor_name not in ACTORS:
        raise ValueError(f"Unknown actor: {actor_name}")

    config = ACTORS[actor_name]
    # Create input builder with custom arguments
    input_builder = config.input_builder(**kwargs)
    # ApifyInputBuilder.from_channel(actor_name, **kwargs)

    return (
        config.id,
        config.memory_mbytes,
        input_builder,
    )
